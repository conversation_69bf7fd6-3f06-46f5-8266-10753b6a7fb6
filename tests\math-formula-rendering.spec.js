import { test, expect } from '@playwright/test';

/**
 * 数学公式渲染测试套件
 * 专门测试"逻辑链暴击⚡（6行定生死）"后面的公式换行问题
 */

test.describe('数学公式渲染测试', () => {
  // 测试前的准备工作
  test.beforeEach(async ({ page }) => {
    // 设置页面超时时间
    page.setDefaultTimeout(60000);
    
    // 监听控制台消息，用于调试
    page.on('console', msg => {
      if (msg.type() === 'error') {
        console.log('页面错误:', msg.text());
      }
    });
    
    // 监听页面错误
    page.on('pageerror', error => {
      console.log('页面异常:', error.message);
    });
  });

  test('基础页面加载测试', async ({ page }) => {
    console.log('🔧 开始基础页面加载测试...');
    
    // 访问指定URL
    await page.goto('/fb/sy?kjid=1_3e_26bha6h&type=syzc&id=123');
    
    // 等待页面加载完成
    await page.waitForLoadState('networkidle');
    
    // 验证页面标题或关键元素
    await expect(page).toHaveTitle(/.*/, { timeout: 30000 });
    
    console.log('✅ 基础页面加载测试完成');
  });

  test('MathJax加载状态检查', async ({ page }) => {
    console.log('🔧 开始MathJax加载状态检查...');
    
    // 访问页面
    await page.goto('/fb/sy?kjid=1_3e_26bha6h&type=syzc&id=123');
    
    // 等待页面加载
    await page.waitForLoadState('networkidle');
    
    // 等待MathJax加载完成
    await page.waitForFunction(() => {
      return window.MathJax && window.MathJax.startup && window.MathJax.startup.document;
    }, { timeout: 60000 });
    
    // 检查MathJax是否正确初始化
    const mathJaxReady = await page.evaluate(() => {
      return window.MathJax && typeof window.MathJax.typesetPromise === 'function';
    });
    
    expect(mathJaxReady).toBe(true);
    
    console.log('✅ MathJax加载状态检查完成');
  });

  test('查找"逻辑链暴击"文本元素', async ({ page }) => {
    console.log('🔧 开始查找"逻辑链暴击"文本元素...');
    
    // 访问页面
    await page.goto('/fb/sy?kjid=1_3e_26bha6h&type=syzc&id=123');
    
    // 等待页面加载
    await page.waitForLoadState('networkidle');
    
    // 等待MathJax渲染完成
    await page.waitForTimeout(3000);
    
    // 查找包含"逻辑链暴击"的文本元素
    const targetText = page.locator('text=逻辑链暴击');
    
    // 验证元素存在
    await expect(targetText).toBeVisible({ timeout: 30000 });
    
    // 获取元素信息
    const elementInfo = await targetText.evaluate(el => ({
      tagName: el.tagName,
      className: el.className,
      textContent: el.textContent,
      innerHTML: el.innerHTML
    }));
    
    console.log('找到目标元素:', elementInfo);
    
    console.log('✅ "逻辑链暴击"文本元素查找完成');
  });

  test('截取页面快照', async ({ page }) => {
    console.log('🔧 开始截取页面快照...');
    
    // 访问页面
    await page.goto('/fb/sy?kjid=1_3e_26bha6h&type=syzc&id=123');
    
    // 等待页面加载和MathJax渲染
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(5000);
    
    // 截取全页面快照
    await page.screenshot({ 
      path: 'test-results/full-page-screenshot.png',
      fullPage: true 
    });
    
    // 截取视口快照
    await page.screenshot({ 
      path: 'test-results/viewport-screenshot.png' 
    });
    
    console.log('✅ 页面快照截取完成');
  });

  test('获取页面HTML源码', async ({ page }) => {
    console.log('🔧 开始获取页面HTML源码...');
    
    // 访问页面
    await page.goto('/fb/sy?kjid=1_3e_26bha6h&type=syzc&id=123');
    
    // 等待页面加载和渲染
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(5000);
    
    // 获取完整的HTML源码
    const htmlContent = await page.content();
    
    // 保存HTML源码到文件
    const fs = require('fs');
    const path = require('path');
    
    // 确保test-results目录存在
    const resultsDir = 'test-results';
    if (!fs.existsSync(resultsDir)) {
      fs.mkdirSync(resultsDir, { recursive: true });
    }
    
    // 保存HTML源码
    fs.writeFileSync(path.join(resultsDir, 'page-source.html'), htmlContent, 'utf8');
    
    // 验证HTML内容包含关键元素
    expect(htmlContent).toContain('逻辑链暴击');
    expect(htmlContent.length).toBeGreaterThan(1000);
    
    console.log('✅ 页面HTML源码获取完成');
  });
});
