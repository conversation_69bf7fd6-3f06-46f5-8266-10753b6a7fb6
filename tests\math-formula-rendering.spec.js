import { expect, test } from '@playwright/test';

/**
 * 数学公式渲染测试套件
 * 专门测试"逻辑链暴击⚡（6行定生死）"后面的公式换行问题
 */

test.describe('数学公式渲染测试', () => {
  // 测试前的准备工作
  test.beforeEach(async ({ page }) => {
    // 设置页面超时时间
    page.setDefaultTimeout(60000);

    // 监听控制台消息，用于调试
    page.on('console', (msg) => {
      if (msg.type() === 'error') {
        console.log('页面错误:', msg.text());
      }
    });

    // 监听页面错误
    page.on('pageerror', (error) => {
      console.log('页面异常:', error.message);
    });
  });

  test('基础页面加载测试', async ({ page }) => {
    console.log('🔧 开始基础页面加载测试...');

    // 访问指定URL
    await page.goto('/fb/sy?kjid=1_3e_26bha6h&type=syzc&id=123');

    // 等待页面加载完成
    await page.waitForLoadState('networkidle');

    // 验证页面标题或关键元素
    await expect(page).toHaveTitle(/.*/, { timeout: 30000 });

    console.log('✅ 基础页面加载测试完成');
  });

  test('MathJax加载状态检查', async ({ page }) => {
    console.log('🔧 开始MathJax加载状态检查...');

    // 访问页面
    await page.goto('/fb/sy?kjid=1_3e_26bha6h&type=syzc&id=123');

    // 等待页面加载
    await page.waitForLoadState('networkidle');

    // 等待MathJax加载完成
    await page.waitForFunction(
      () => {
        return window.MathJax && window.MathJax.startup && window.MathJax.startup.document;
      },
      { timeout: 60000 },
    );

    // 检查MathJax是否正确初始化
    const mathJaxReady = await page.evaluate(() => {
      return window.MathJax && typeof window.MathJax.typesetPromise === 'function';
    });

    expect(mathJaxReady).toBe(true);

    console.log('✅ MathJax加载状态检查完成');
  });

  test('查找"逻辑链暴击"文本元素', async ({ page }) => {
    console.log('🔧 开始查找"逻辑链暴击"文本元素...');

    // 访问页面
    await page.goto('/fb/sy?kjid=1_3e_26bha6h&type=syzc&id=123');

    // 等待页面加载
    await page.waitForLoadState('networkidle');

    // 等待MathJax渲染完成
    await page.waitForTimeout(3000);

    // 查找包含"逻辑链暴击"的文本元素
    const targetText = page.locator('text=逻辑链暴击');

    // 验证元素存在
    await expect(targetText).toBeVisible({ timeout: 30000 });

    // 获取元素信息
    const elementInfo = await targetText.evaluate((el) => ({
      tagName: el.tagName,
      className: el.className,
      textContent: el.textContent,
      innerHTML: el.innerHTML,
    }));

    console.log('找到目标元素:', elementInfo);

    console.log('✅ "逻辑链暴击"文本元素查找完成');
  });

  test('深入分析"逻辑链暴击"后的数学公式', async ({ page }) => {
    console.log('🔧 开始深入分析"逻辑链暴击"后的数学公式...');

    // 导入Node.js模块
    const fs = require('fs');
    const path = require('path');

    // 确保test-results目录存在
    const resultsDir = 'test-results';
    if (!fs.existsSync(resultsDir)) {
      fs.mkdirSync(resultsDir, { recursive: true });
    }

    // 访问页面
    await page.goto('/fb/sy?kjid=1_3e_26bha6h&type=syzc&id=123');

    // 等待页面加载
    await page.waitForLoadState('networkidle');

    // 等待更长时间确保MathJax完全渲染
    await page.waitForTimeout(8000);

    // 查找"逻辑链暴击"元素
    const targetText = page.locator('text=逻辑链暴击');
    await expect(targetText).toBeVisible({ timeout: 30000 });

    // 获取"逻辑链暴击"元素的父容器
    const parentContainer = await targetText.evaluate((el) => {
      // 向上查找包含数学公式的容器
      let parent = el.parentElement;
      while (parent && !parent.querySelector('.mjx-container, mjx-container')) {
        parent = parent.parentElement;
        if (parent === document.body) break;
      }
      return parent ? parent.outerHTML : null;
    });

    if (parentContainer) {
      console.log('找到包含数学公式的父容器');
      console.log('父容器HTML长度:', parentContainer.length);

      // 保存父容器HTML到文件
      fs.writeFileSync(
        path.join(resultsDir, 'logic-chain-container.html'),
        parentContainer,
        'utf8',
      );
    }

    // 查找"逻辑链暴击"后面的所有MathJax容器
    const mathContainers = await page.locator('.mjx-container').all();
    console.log(`找到 ${mathContainers.length} 个MathJax容器`);

    // 分析每个数学公式容器
    for (let i = 0; i < mathContainers.length; i++) {
      const container = mathContainers[i];

      // 检查是否在"逻辑链暴击"附近
      const isNearTarget = await container.evaluate((el, targetText) => {
        const targetEl = document.querySelector('strong:has-text("逻辑链暴击")');
        if (!targetEl) return false;

        // 计算元素间的距离
        const targetRect = targetEl.getBoundingClientRect();
        const mathRect = el.getBoundingClientRect();

        // 如果数学公式在目标文本下方500px内，认为是相关的
        return mathRect.top > targetRect.top && mathRect.top - targetRect.bottom < 500;
      }, 'targetText');

      if (isNearTarget) {
        console.log(`🎯 找到"逻辑链暴击"附近的数学公式容器 ${i}`);

        // 获取详细信息
        const mathInfo = await container.evaluate((el) => {
          const computedStyle = window.getComputedStyle(el);
          return {
            innerHTML: el.innerHTML,
            outerHTML: el.outerHTML,
            clientWidth: el.clientWidth,
            clientHeight: el.clientHeight,
            fontSize: computedStyle.fontSize,
            lineHeight: computedStyle.lineHeight,
            whiteSpace: computedStyle.whiteSpace,
            wordWrap: computedStyle.wordWrap,
            overflow: computedStyle.overflow,
            display: computedStyle.display,
          };
        });

        console.log(`数学公式 ${i} 详细信息:`, {
          尺寸: `${mathInfo.clientWidth}x${mathInfo.clientHeight}`,
          字体大小: mathInfo.fontSize,
          行高: mathInfo.lineHeight,
          空白处理: mathInfo.whiteSpace,
          换行: mathInfo.wordWrap,
          溢出: mathInfo.overflow,
          显示模式: mathInfo.display,
        });

        // 保存数学公式HTML
        fs.writeFileSync(
          path.join(resultsDir, `math-formula-${i}.html`),
          mathInfo.outerHTML,
          'utf8',
        );
        console.log(`数学公式 ${i} HTML已保存，长度:`, mathInfo.outerHTML.length);

        // 检查是否包含SVG（MathJax SVG输出）
        const svgInfo = await container.evaluate((el) => {
          const svg = el.querySelector('svg');
          if (svg) {
            return {
              viewBox: svg.getAttribute('viewBox'),
              width: svg.getAttribute('width'),
              height: svg.getAttribute('height'),
              style: svg.getAttribute('style'),
            };
          }
          return null;
        });

        if (svgInfo) {
          console.log(`数学公式 ${i} SVG信息:`, svgInfo);
        }
      }
    }

    console.log('✅ "逻辑链暴击"后的数学公式分析完成');
  });

  test('截取页面快照', async ({ page }) => {
    console.log('🔧 开始截取页面快照...');

    // 访问页面
    await page.goto('/fb/sy?kjid=1_3e_26bha6h&type=syzc&id=123');

    // 等待页面加载和MathJax渲染
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(5000);

    // 截取全页面快照
    await page.screenshot({
      path: 'test-results/full-page-screenshot.png',
      fullPage: true,
    });

    // 截取视口快照
    await page.screenshot({
      path: 'test-results/viewport-screenshot.png',
    });

    console.log('✅ 页面快照截取完成');
  });

  test('获取页面HTML源码和原始数据', async ({ page }) => {
    console.log('🔧 开始获取页面HTML源码和原始数据...');

    // 访问页面
    await page.goto('/fb/sy?kjid=1_3e_26bha6h&type=syzc&id=123');

    // 等待页面加载和渲染
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(8000);

    // 获取完整的HTML源码
    const htmlContent = await page.content();

    // 保存HTML源码到文件
    const fs = require('fs');
    const path = require('path');

    // 确保test-results目录存在
    const resultsDir = 'test-results';
    if (!fs.existsSync(resultsDir)) {
      fs.mkdirSync(resultsDir, { recursive: true });
    }

    // 保存HTML源码
    fs.writeFileSync(path.join(resultsDir, 'page-source.html'), htmlContent, 'utf8');

    // 获取Vue组件的原始数据
    const vueData = await page.evaluate(() => {
      // 尝试获取Vue实例的数据
      const app = document.querySelector('#app');
      if (app && app.__vue__) {
        return {
          hasVueInstance: true,
          data: app.__vue__.$data,
        };
      }

      // 尝试获取页面中的原始Markdown内容
      const scripts = Array.from(document.querySelectorAll('script'));
      const dataScript = scripts.find(
        (script) => script.textContent && script.textContent.includes('逻辑链暴击'),
      );

      if (dataScript) {
        return {
          hasVueInstance: false,
          rawScript: dataScript.textContent,
        };
      }

      return { hasVueInstance: false, rawScript: null };
    });

    console.log('Vue数据获取结果:', vueData);

    // 保存Vue数据
    fs.writeFileSync(
      path.join(resultsDir, 'vue-data.json'),
      JSON.stringify(vueData, null, 2),
      'utf8',
    );

    // 查找"逻辑链暴击"在HTML中的具体位置和上下文
    const logicChainContext = await page.evaluate(() => {
      const walker = document.createTreeWalker(document.body, NodeFilter.SHOW_TEXT, null, false);

      const contexts = [];
      let node;

      while ((node = walker.nextNode())) {
        if (node.textContent.includes('逻辑链暴击')) {
          const parent = node.parentElement;
          contexts.push({
            textContent: node.textContent,
            parentTagName: parent.tagName,
            parentClassName: parent.className,
            parentInnerHTML: parent.innerHTML,
            nextSibling: parent.nextElementSibling
              ? {
                  tagName: parent.nextElementSibling.tagName,
                  className: parent.nextElementSibling.className,
                  innerHTML: parent.nextElementSibling.innerHTML.substring(0, 500) + '...',
                }
              : null,
          });
        }
      }

      return contexts;
    });

    console.log('找到"逻辑链暴击"的上下文:', logicChainContext);

    // 保存上下文信息
    fs.writeFileSync(
      path.join(resultsDir, 'logic-chain-context.json'),
      JSON.stringify(logicChainContext, null, 2),
      'utf8',
    );

    // 验证HTML内容包含关键元素
    expect(htmlContent).toContain('逻辑链暴击');
    expect(htmlContent.length).toBeGreaterThan(1000);

    console.log('✅ 页面HTML源码和原始数据获取完成');
  });
});
